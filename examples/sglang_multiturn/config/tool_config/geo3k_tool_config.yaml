tools:
  - class_name: "verl.tools.geo3k_tool.Geo3kTool"
    config: 
      type: native
    tool_schema:
      type: "function"
      function:
        name: "calc_geo3k_reward"
        description: "A tool for calculating the reward of geo3k. (1.0 if parsed answer is correct, 0.0 if parsed answer is incorrect or not correctly parsed)"
        parameters:
          type: "object"
          properties:
            answer:
              type: "string"
              description: "The model's answer to the geo3k problem, must be a digits"
          required: ["answer"]