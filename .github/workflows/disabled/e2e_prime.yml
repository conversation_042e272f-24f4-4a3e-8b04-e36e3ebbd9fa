name: e2e_prime

on:
  # Trigger the workflow on push or pull request,
  # but only for the main branch
  push:
    branches:
      - disabled_ci
  pull_request:
    branches:
      - disabled_ci
    paths:
      - "**/*.py"
      # Other entrypoints
      - "!examples/**"
      - "!tests/**"
      - "!verl/trainer/main_*.py"
      - "!verl/trainer/fsdp_sft_trainer.py"
      # Other recipes
      - "!recipe/**"
      # Megatron
      - "!verl/workers/**/megatron_*.py"
      # Home
      - "recipe/prime"
      # Entrypoints
      - ".github/workflows/e2e_prime.yml"
      - "examples/data_preprocess/gsm8k.py"
      - "tests/special_e2e/run_prime.sh"

# Cancel jobs on the same ref if a new one is triggered
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ github.ref != 'refs/heads/main' }}

# Declare permissions just read content.
permissions:
  contents: read

jobs:
  e2e_prime:
    runs-on: [L20x8]
    timeout-minutes: 50 # Increase this timeout value as needed
    env:
      HTTP_PROXY: ${{ secrets.PROXY_HTTP }}
      HTTPS_PROXY: ${{ secrets.PROXY_HTTPS }}
      NO_PROXY: "localhost,127.0.0.1,hf-mirror.com"
      HF_ENDPOINT: "https://hf-mirror.com"
      HF_HUB_ENABLE_HF_TRANSFER: "0" # This is more stable
    container:
      image: whatcanyousee/verl:ngc-cu124-vllm0.8.5-sglang0.4.6.post5-mcore0.12.0-te2.3
      options: --gpus all --shm-size=10g
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0
      - name: Install the current repository
        run: |
          pip3 install --no-deps -e .[test,gpu]
      - name: Prepare gsm8k dataset
        run: |
          ray stop --force
          python3 examples/data_preprocess/gsm8k.py
      - name: Running GSM8K E2E with prime alg
        run: |
          ray stop --force
          bash tests/special_e2e/run_prime.sh
