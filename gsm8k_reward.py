# GSM8K Math Problem Reward Function
# Copyright 2024 Bytedance Ltd. and/or its affiliates

import re
import logging

logger = logging.getLogger(__name__)

def extract_answer(solution_str, method="flexible"):
    """
    从解题过程中提取最终答案
    
    Args:
        solution_str: 模型生成的解题过程
        method: 提取方法，"strict" 或 "flexible"
    
    Returns:
        str or None: 提取的数值答案
    """
    try:
        if method == "strict":
            # 严格模式：必须遵循 "#### answer" 格式
            pattern = r"#### ([\-\+]?\d+(?:\.\d+)?)"
            match = re.search(pattern, solution_str)
            if match:
                return match.group(1).replace(",", "").replace("$", "")
            return None
        
        elif method == "flexible":
            # 灵活模式：提取最后一个有效数字作为答案
            # 寻找所有数字（包括小数和负数）
            numbers = re.findall(r"[\-\+]?\d+(?:\.\d+)?", solution_str)
            if not numbers:
                return None
            
            # 过滤掉无效的数字字符串
            invalid_patterns = ["", ".", "0.0", "0"]
            valid_numbers = [num for num in numbers if num not in invalid_patterns]
            
            if valid_numbers:
                # 返回最后一个有效数字
                return valid_numbers[-1]
            
            return None
        
        else:
            raise ValueError(f"Unknown extraction method: {method}")
    except Exception as e:
        logger.error(f"Error in extract_answer: {e}")
        return None

def normalize_answer(answer_str):
    """
    标准化答案格式，移除空格、逗号、美元符号等
    """
    try:
        if answer_str is None:
            return None
        
        answer_str = str(answer_str).strip()
        answer_str = answer_str.replace(",", "").replace("$", "").replace(" ", "")
        
        try:
            # 尝试转换为浮点数再转回字符串，以统一格式
            num = float(answer_str)
            # 如果是整数，返回整数格式
            if num == int(num):
                return str(int(num))
            else:
                return str(num)
        except ValueError:
            return answer_str
    except Exception as e:
        logger.error(f"Error in normalize_answer: {e}")
        return None

def compute_score(data_source, solution_str, ground_truth, extra_info=None, **kwargs):
    """
    计算GSM8K数学问题的reward分数
    
    Args:
        data_source: 数据源标识
        solution_str: 模型生成的解题过程
        ground_truth: 正确答案
        extra_info: 额外信息
    
    Returns:
        float: 分数值 (为了避免数据不一致问题，只返回单一数值)
    """
    
    try:
        # 基础验证
        if not solution_str or not ground_truth:
            return 0.0
        
        # 对于非GSM8K数据源，使用默认处理
        if data_source != "openai/gsm8k" and not data_source.endswith("gsm8k"):
            logger.warning(f"Using GSM8K reward function for non-GSM8K data source: {data_source}")
        
        # 提取模型生成的答案
        predicted_answer = extract_answer(solution_str, method="flexible")
        
        # 标准化答案
        pred_normalized = normalize_answer(predicted_answer)
        truth_normalized = normalize_answer(ground_truth)
        
        # 判断正确性
        if pred_normalized is None:
            # 没有找到有效答案
            score = 0.0
        elif pred_normalized == truth_normalized:
            # 答案正确
            score = 1.0
        else:
            # 答案错误但有格式
            score = 0.1  # 给予小的格式奖励
        
        # 记录调试信息
        logger.debug(f"GSM8K Scoring - Pred: {pred_normalized}, Truth: {truth_normalized}, Score: {score}")
        
        return score
        
    except Exception as e:
        logger.error(f"Error in compute_score: {e}")
        # 确保在任何错误情况下都返回数值
        return 0.0

def compute_score_batched(data_sources, solution_strs, ground_truths, extra_infos=None, **kwargs):
    """
    批量计算GSM8K数学问题的reward分数，确保数据一致性
    
    Args:
        data_sources: 数据源标识列表
        solution_strs: 模型生成的解题过程列表
        ground_truths: 正确答案列表
        extra_infos: 额外信息列表
    
    Returns:
        list: 包含分数的列表
    """
    
    batch_size = len(solution_strs)
    results = []
    
    # 确保所有输入参数的长度一致
    assert len(data_sources) == batch_size, f"data_sources length {len(data_sources)} != batch_size {batch_size}"
    assert len(ground_truths) == batch_size, f"ground_truths length {len(ground_truths)} != batch_size {batch_size}"
    
    if extra_infos is None:
        extra_infos = [None] * batch_size
    else:
        assert len(extra_infos) == batch_size, f"extra_infos length {len(extra_infos)} != batch_size {batch_size}"
    
    # 逐一处理每个样本
    for i in range(batch_size):
        try:
            result = compute_score(
                data_source=data_sources[i],
                solution_str=solution_strs[i],
                ground_truth=ground_truths[i],
                extra_info=extra_infos[i],
                **kwargs
            )
            results.append(result)
        except Exception as e:
            logger.error(f"Error processing sample {i}: {e}")
            # 即使某个样本失败，也要添加默认结果以保持长度一致
            results.append(0.0)
    
    # 最终检查：确保结果长度与输入长度一致
    assert len(results) == batch_size, f"Results length {len(results)} != batch_size {batch_size}"
    
    return results

# 向后兼容的简单函数
def compute_gsm8k_score(solution_str, ground_truth, method="flexible"):
    """
    简化的GSM8K评分函数，向后兼容
    """
    return compute_score("openai/gsm8k", solution_str, ground_truth) 